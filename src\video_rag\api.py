from typing import Optional
from fastapi import APIRouter, File, UploadFile, HTTPException, status, Query, Body
from src.models import Video
from src.core.dependencies import (
    MinioServiceDep,
    LLMServiceDep,
    DifyServiceDep,
    VideoAPISettingsDep
)
from pydantic import BaseModel
import json
import os
import math
import asyncio
import time

video_router = APIRouter()




# 获取视频列表（带分页）
@video_router.get("/list")
async def list_video(
    page: int = Query(1, ge=1, description="页码，从1开始"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量，最大100"),
    
):
    try:
        # 计算偏移量
        offset = (page - 1) * page_size

        # 查询总数
        total = await Video.all().count()

        # 查询当前页的视频记录
        videos_raw = await Video.all().offset(offset).limit(page_size)
        
        # 格式化视频数据，确保datetime字段正确序列化
        videos = []
        for video in videos_raw:
            video_dict = {
                "id": video.id,
                "title": video.title,
                "description": video.description,
                "video_url": video.video_url,
                "file_size": video.file_size,
                "categories": video.categories,
                "tags": video.tags,
                "status": video.status,
                "created_at": video.created_at.strftime('%Y-%m-%d %H:%M:%S') if video.created_at else None,
                "updated_at": video.updated_at.strftime('%Y-%m-%d %H:%M:%S') if video.updated_at else None
            }
            videos.append(video_dict)

        # 计算总页数
        total_pages = math.ceil(total / page_size) if total > 0 else 1

        return {
            "code": 200,
            "message": "获取成功",
            "videos": videos,
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": total_pages
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取视频列表失败: {str(e)}"
        )




# 用户上传视频
@video_router.post("/upload_video")
async def upload_video(
    file: UploadFile = File(...),
    minio_service: MinioServiceDep = None,
    video_settings: VideoAPISettingsDep = None
):
    
    try:
        
        # 验证视频后缀
        file_extension = os.path.splitext(file.filename)[1].lower()
        if file_extension != ".mp4":
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="只支持MP4格式的视频文件")
        
        
        # 异步读取
        file_content = await file.read()
        file_length = len(file_content)
        
        # 验证视频大小
        MAX_VIDEO_SIZE = 1024 * 1024 * video_settings.MAX_VIDEO_SIZE_MB
        if file_length > MAX_VIDEO_SIZE:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"视频大小超过限制，最大允许大小为{video_settings.MAX_VIDEO_SIZE_MB}MB"
            )

        # 上传视频到minio（异步调用）
        video_url = await minio_service.upload_file(
            bucket_name=video_settings.BUCKET_NAME,
            object_name=f'videos/{file.filename}',
            file_stream=file_content,
            length=file_length,
            content_type=file.content_type
        )
        
        if video_url:
            
            # 调用多模态大模型分析视频内容
            # description = await llm_service.analyze_multimodal_campaign(video_url)
            # 根据文件名称查询现有视频记录
            video_entry = await Video.get_or_none(title=file.filename)
            try:
                if video_entry:
                    # 如果视频存在则更新视频记录
                    video_entry.title = file.filename
                    video_entry.description = ""
                    video_entry.file_size = file_length
                    video_entry.categories = []
                    video_entry.tags = []
                    video_entry.status = "pending"
                    
                    await video_entry.save()
                    message = f"文件已经覆盖，更新数据库记录为待处理状态"
                    
                else:
                    # 如果视频不存在则新建视频记录
                    video_entry = await Video.create(
                        title=file.filename,
                        video_url=video_url,
                        description="",
                        file_size=file_length,
                        categories=[],
                        tags=[],
                        status="pending"
                    )
                    
                    message = "文件上传成功，成功创建数据库记录"
                    
                return {
                    "code": 200, 
                    "message": message, 
                    "title": file.filename, 
                    "video_url": video_url.strip() if video_url else ""
                }
            except Exception as e:
                raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"更新数据库记录失败: {e}")
            
        else:
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="上传失败")
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"上传失败: {e}")



# 进行视频分类，分析视频主要内容提取标签
class VideoAnalysisRequest(BaseModel):
    title: str
    video_url: str
    description: Optional[str] = ""

@video_router.post("/parse_video")
async def parse_video(
    request: VideoAnalysisRequest,
    llm_service: LLMServiceDep
):
    """
    调用多模态大模型对视频内容进行解析 - 改进版本，支持超时控制和更好的资源管理
    """
    task_id = f"{request.video_url}_{int(time.time())}"

    try:
        print(f"[{task_id}] 开始解析视频: {request.title}")

        # 检查视频记录是否存在
        video_entry = await Video.get_or_none(video_url=request.video_url)
        if not video_entry:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"视频记录不存在: {request.video_url}"
            )

        # 检查视频是否已经在处理中或已完成
        if video_entry.status in ["processing", "completed"]:
            print(f"[{task_id}] 视频已经在处理中或已完成，状态: {video_entry.status}")
            return {
                "code": 200,
                "message": f"视频已经在处理中或已完成，当前状态: {video_entry.status}",
                "response": video_entry.description or "正在处理中..."
            }

        # 简化版本：直接进行分析，不使用复杂的并发控制
        print(f"[{task_id}] 更新状态为processing")
        video_entry.status = "processing"
        await video_entry.save()

        try:
            # 使用依赖注入的LLM服务
            print(f"[{task_id}] 开始视频分析")
            response = await llm_service.analyze_multimodal_campaign(request.video_url)

            if not response:
                raise ValueError("LLM返回空响应")

            print(f"[{task_id}] 分析完成，响应长度: {len(response)}")

            # 更新视频记录
            video_entry.description = response
            video_entry.status = "processing"  # 保持processing状态
            await video_entry.save()

            print(f"[{task_id}] 数据库更新完成")

            return {
                "code": 200,
                "message": "视频分析成功，数据库状态已更新为processing",
                "response": response
            }

        except Exception as analysis_error:
            print(f"[{task_id}] 分析失败: {str(analysis_error)}")
            print(f"[{task_id}] 异常类型: {type(analysis_error).__name__}")

            # 更新状态为失败
            video_entry.status = "failed"
            await video_entry.save()

            # 安全的异常处理
            error_str = str(analysis_error)
            if error_str == request.video_url or request.video_url in error_str:
                safe_error = f"视频分析过程中发生异常，异常类型: {type(analysis_error).__name__}"
            else:
                safe_error = error_str

            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"视频分析失败: {safe_error}"
            )

    except HTTPException:
        raise
    except Exception as e:
        print(f"[{task_id}] 视频分析失败: {str(e)}")
        try:
            video_entry = await Video.get_or_none(video_url=request.video_url)
            if video_entry:
                video_entry.status = "failed"
                await video_entry.save()
        except:
            pass

        # 最后一层保护：确保不会返回异常的错误消息
        error_str = str(e)
        if error_str == request.video_url or error_str == f"'{request.video_url}'" or request.video_url in error_str:
            print(f"[{task_id}] 最终警告：异常消息包含视频URL，使用安全错误消息")
            safe_error_message = f"视频分析失败，异常类型: {type(e).__name__}"
        else:
            safe_error_message = error_str

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"视频分析失败: {safe_error_message}"
        )





    
@video_router.post("/analyze_video")
async def analyze_video(
    request: VideoAnalysisRequest,
    llm_service: LLMServiceDep
):
    """
    调用LLM模型对视频内容进行解析优化，生成视频摘要，分类和标签
    """
    try:
        # 使用依赖注入的LLM服务
        # 调用LLM进行内容优化
        response = await llm_service.refine_video_content(request.title, request.description)

        if response:
            # 更新视频记录
            video_entry = await Video.get_or_none(video_url=request.video_url)
            if video_entry:
                video_entry.description = response["description"]
                video_entry.categories = response["categories"]
                video_entry.tags = response["tags"]
                video_entry.status = "processing"

                await video_entry.save()
                message = "视频分析成功，数据库状态已更新"
            else:
                message = "视频分析成功，但未找到对应的数据库记录"

            return {"code": 200, "message": message, "response": response}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="LLM返回空响应"
            )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"视频分析失败: {e}"
        )
    finally:
        # 清理资源
        try:
            del llm_service
        except:
            pass




# 获取视频信息API
@video_router.get("/info/{video_id}")
async def get_video_info(video_id: str):
    """
    根据视频ID获取视频基础信息
    Args:
        video_id (str): 视频ID (通常是文件名)

    Returns:
        dict: 视频基础信息
    """
    try:
        # 构建video_url (假设所有视频都在同一个bucket下)
        video_title = f"{video_id}"

        # 根据video_url查询视频记录
        video_entry = await Video.get_or_none(title=video_title)

        if not video_entry:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"视频 {video_id} 不存在"
            )

        return {
            "code": 200,
            "message": "获取成功",
            "data": {
                "title": video_entry.title,
                "video_url": video_entry.video_url,
                "description": video_entry.description,
                "categories": video_entry.categories,
                "tags": video_entry.tags,
                "status": video_entry.status
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取视频信息失败: {str(e)}"
        )


# 提交视频元数据到Dify知识库
class SubmitVideoToDifyRequest(BaseModel):
    title: str
    description: str
    categories: list[str]
    tags: list[str]
    video_url: str

    # 添加数据验证
    class Config:
        json_encoders = {
            str: lambda v: v.strip() if isinstance(v, str) else v
        }

    def validate_payload(self):
        """验证请求数据格式和内容"""
        errors = []

        # 验证必填字段
        if not self.title or not self.title.strip():
            errors.append("title字段不能为空")

        if not self.description or not self.description.strip():
            errors.append("description字段不能为空")
        elif len(self.description.strip()) > 2000:
            errors.append("description字段长度不能超过2000字符")

        if not self.categories or len(self.categories) == 0:
            errors.append("categories字段不能为空")
        elif len(self.categories) < 2 or len(self.categories) > 5:
            errors.append("categories字段应包含2-5个分类")
        elif any(not cat.strip() for cat in self.categories):
            errors.append("categories中不能包含空字符串")

        if not self.tags or len(self.tags) == 0:
            errors.append("tags字段不能为空")
        elif len(self.tags) < 3 or len(self.tags) > 10:
            errors.append("tags字段应包含3-10个标签")
        elif any(not tag.strip() for tag in self.tags):
            errors.append("tags中不能包含空字符串")

        if not self.video_url or not self.video_url.strip():
            errors.append("video_url字段不能为空")
        elif not (self.video_url.startswith('http://') or self.video_url.startswith('https://')):
            errors.append("video_url必须是有效的HTTP/HTTPS URL")

        return errors

@video_router.post("/submit_to_dify")
async def submit_video_to_dify(
    request: SubmitVideoToDifyRequest,
    dify_service: DifyServiceDep = None
):
    """
    将视频元数据提交到Dify知识库

    Required JSON Format:
    {
        "title": "string - Video filename or descriptive title",
        "description": "string - Detailed video content description (max 2000 chars)",
        "categories": ["array of 2-5 strings - Main topic classifications"],
        "tags": ["array of 3-10 strings - Specific keywords and technical terms"],
        "video_url": "string - Complete HTTP/HTTPS URL to the video file"
    }

    Args:
        request: 标准化的视频元数据请求

    Returns:
        提交结果
    """
    try:
        # 验证请求数据格式
        validation_errors = request.validate_payload()
        if validation_errors:
            return {
                "code": 400,
                "message": "请求数据格式错误",
                "errors": validation_errors
            }

        # 准备标准化的视频数据
        video_data = {
            "title": request.title.strip(),
            "description": request.description.strip(),
            "categories": [cat.strip() for cat in request.categories],
            "tags": [tag.strip() for tag in request.tags],
            "video_url": request.video_url.strip()
        }

        # 提交到Dify知识库
        result = await dify_service.submit_video_to_knowledge_base(video_data)

        if result["success"]:
            # 更新数据库中的视频状态为已完成
            video_entry = await Video.get_or_none(video_url=request.video_url)
            if video_entry:
                video_entry.status = "completed"
                await video_entry.save()

            return {
                "code": 200,
                "message": result["message"],
                "data": {
                    "knowledge_base_id": result["knowledge_base_id"],
                    "document_id": result["document_id"],
                    "submitted_payload": video_data
                }
            }
        else:
            return {
                "code": 500,
                "message": result["message"],
                "error": result.get("error", "未知错误")
            }

    except Exception as e:
        return {
            "code": 500,
            "message": f"提交到知识库失败: {str(e)}",
            "error": str(e)
        }


# 提交信息到AI知识库 (保留原有接口以兼容)
class SubmitVideoRequest(BaseModel):
    title: str
    video_url: str
    description: str
    keywords: list
    tags: list

@video_router.post("/submit_video_to_rag", deprecated=True)
async def submit_video_to_rag(request: SubmitVideoRequest):
    """
    兼容性接口
    废弃，将在下一版本中删除
    """
    _ = request  # 标记参数已使用
    return {"code": 200, "message": "视频提交成功", "response": "视频提交成功"}

@video_router.get("/verify_dify_document/{knowledge_base_id}/{document_id}")
async def verify_dify_document(
    knowledge_base_id: str,
    document_id: str,
    dify_service: DifyServiceDep = None
):
    """
    验证Dify知识库中的文档内容
    Args:
        knowledge_base_id (str): 知识库ID
        document_id (str): 文档ID

    Returns:
        dict: 文档验证结果
    """
    try:
        # 获取文档详情
        doc_details = await dify_service._make_request("GET", f"/datasets/{knowledge_base_id}/documents/{document_id}")

        # 获取文档段落
        segments_response = await dify_service._make_request("GET", f"/datasets/{knowledge_base_id}/documents/{document_id}/segments")
        segments = segments_response.get('data', [])

        result = {
            "code": 200,
            "message": "文档验证成功",
            "data": {
                "document_info": {
                    "id": doc_details.get('id'),
                    "name": doc_details.get('name'),
                    "status": doc_details.get('indexing_status'),
                    "character_count": doc_details.get('character_count', 0),
                    "created_at": doc_details.get('created_at')
                },
                "segments_count": len(segments),
                "segments": []
            }
        }

        # 处理段落内容
        for i, segment in enumerate(segments):
            content = segment.get('content', '')
            segment_info = {
                "index": i + 1,
                "content_length": len(content),
                "content": content
            }

            # 尝试解析JSON内容
            try:
                json_content = json.loads(content)
                segment_info["is_valid_json"] = True
                segment_info["json_fields"] = list(json_content.keys())
                segment_info["parsed_content"] = json_content
            except json.JSONDecodeError:
                segment_info["is_valid_json"] = False
                segment_info["content_preview"] = content[:200] + "..." if len(content) > 200 else content

            result["data"]["segments"].append(segment_info)

        return result

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"验证文档失败: {str(e)}"
        )


# 测试标准化JSON格式的端点
@video_router.post("/test_dify_format")
async def test_dify_format(request: SubmitVideoToDifyRequest):
    """
    测试标准化JSON格式，验证数据结构
    """
    try:
        # 验证请求数据格式
        validation_errors = request.validate_payload()
        if validation_errors:
            return {
                "code": 400,
                "message": "请求数据格式错误",
                "errors": validation_errors
            }

        # 准备标准化的视频数据
        video_data = {
            "title": request.title.strip(),
            "description": request.description.strip(),
            "categories": [cat.strip() for cat in request.categories],
            "tags": [tag.strip() for tag in request.tags],
            "video_url": request.video_url.strip()
        }

        return {
            "code": 200,
            "message": "标准化JSON格式验证成功",
            "data": {
                "validated_payload": video_data,
                "payload_info": {
                    "title_length": len(video_data["title"]),
                    "description_length": len(video_data["description"]),
                    "categories_count": len(video_data["categories"]),
                    "tags_count": len(video_data["tags"]),
                    "url_valid": video_data["video_url"].startswith(('http://', 'https://'))
                }
            }
        }

    except Exception as e:
        return {
            "code": 500,
            "message": f"验证失败: {str(e)}",
            "error": str(e)
        }


# 系统健康检查和资源管理端点
@video_router.get("/health")
async def health_check(video_settings: VideoAPISettingsDep = None):
    """系统健康检查"""
    return {
        "code": 200,
        "message": "系统运行正常",
        "data": {
            "max_concurrent_analysis": video_settings.MAX_CONCURRENT_ANALYSIS,
            "analysis_timeout": video_settings.ANALYSIS_TIMEOUT,
            "max_video_size_mb": video_settings.MAX_VIDEO_SIZE_MB,
            "bucket_name": video_settings.BUCKET_NAME
        }
    }

# @video_router.get("/debug/config")
# async def debug_config():
#     """调试配置信息"""
#     import os
#     return {
#         "code": 200,
#         "message": "配置信息",
#         "data": {
#             "video_api_settings": {
#                 "BUCKET_NAME": video_api_settings.BUCKET_NAME,
#                 "MAX_CONCURRENT_ANALYSIS": video_api_settings.MAX_CONCURRENT_ANALYSIS,
#                 "ANALYSIS_TIMEOUT": video_api_settings.ANALYSIS_TIMEOUT,
#                 "MAX_VIDEO_SIZE_MB": video_api_settings.MAX_VIDEO_SIZE_MB
#             },
#             "minio_settings": {
#                 "MINIO_ENDPOINT": minio_service.endpoint,
#                 "MINIO_ACCESS_KEY": minio_service.access_key,
#                 "MINIO_SECURE": minio_service.secure
#             },
#             "environment_variables": {
#                 "BUCKET_NAME": os.getenv("BUCKET_NAME", "未设置"),
#                 "MINIO_ENDPOINT": os.getenv("MINIO_ENDPOINT", "未设置"),
#                 "MINIO_ACCESS_KEY": os.getenv("MINIO_ACCESS_KEY", "未设置")
#             }
#         }
#     }

@video_router.post("/cleanup")
async def cleanup_resources():
    """清理系统资源"""
    try:
        # 简化版本：只返回清理状态
        return {
            "code": 200,
            "message": "资源清理完成",
            "data": {
                "message": "当前版本使用简化的资源管理，无需手动清理"
            }
        }
    except Exception as e:
        return {
            "code": 500,
            "message": f"资源清理失败: {str(e)}"
        }

@video_router.delete("/delete/{video_id}")
async def delete_video(
    video_id: str,
    dify_service: DifyServiceDep = None
):
    """
    删除视频记录和相关的Dify知识库文档
    Args:
        video_id (str): 视频ID (通常是文件名)

    Returns:
        dict: 删除结果
    """
    try:
        print(f"开始删除视频: {video_id}")

        # 1. 查找视频记录
        video_entry = await Video.get_or_none(title=video_id)

        if not video_entry:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"视频 {video_id} 不存在"
            )

        print(f"找到视频记录: {video_entry.title}, 状态: {video_entry.status}")

        # 2. 如果视频已提交到知识库，则删除知识库中的文档
        dify_deletion_result = None
        if video_entry.status == "completed":
            print(f"视频已提交到知识库，开始删除知识库文档...")

            # 查找知识库中的文档
            doc_search_result = await dify_service.find_document_by_video_title(video_entry.title)

            if doc_search_result.get("found"):
                kb_id = doc_search_result["knowledge_base_id"]
                doc_id = doc_search_result["document_id"]

                print(f"找到知识库文档: KB={kb_id}, Doc={doc_id}")

                # 删除知识库文档
                dify_deletion_result = await dify_service.delete_document_from_knowledge_base(kb_id, doc_id)

                if not dify_deletion_result.get("success"):
                    print(f"知识库文档删除失败: {dify_deletion_result.get('message')}")
                    # 注意：即使知识库删除失败，我们仍然继续删除数据库记录
                else:
                    print(f"知识库文档删除成功")
            else:
                print(f"未找到对应的知识库文档: {doc_search_result.get('message')}")
        else:
            print(f"视频状态为 {video_entry.status}，无需删除知识库文档")

        # 3. 删除数据库记录
        print(f"删除数据库记录...")
        await video_entry.delete()
        print(f"数据库记录删除成功")

        # 4. 构建响应
        result = {
            "code": 200,
            "message": "视频删除成功",
            "data": {
                "video_id": video_id,
                "database_deleted": True,
                "dify_document_deleted": dify_deletion_result.get("success", False) if dify_deletion_result else False,
                "dify_deletion_message": dify_deletion_result.get("message") if dify_deletion_result else "无需删除知识库文档"
            }
        }

        print(f"视频删除完成: {video_id}")
        return result

    except HTTPException:
        raise
    except Exception as e:
        print(f"删除视频失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除视频失败: {str(e)}"
        )

