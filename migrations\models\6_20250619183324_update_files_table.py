from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE `documents` ADD `batch_id` INT;
        ALTER TABLE `documents` ADD `content_type` VARCHAR(255);
        ALTER TABLE `documents` ADD `file_size` BIGINT NOT NULL;
        CREATE TABLE IF NOT EXISTS `document_batches` (
    `id` INT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `batch_uuid` CHAR(36) NOT NULL UNIQUE,
    `batch_name` VARCHAR(255),
    `total_files` INT NOT NULL DEFAULT 0,
    `uploaded_files` INT NOT NULL DEFAULT 0,
    `completed_files` INT NOT NULL DEFAULT 0,
    `failed_files` INT NOT NULL DEFAULT 0,
    `batch_status` VARCHAR(14) NOT NULL COMMENT 'UPLOADING: uploading\nPROCESSING: processing\nCOMPLETED: completed\nPARTIAL_FAILED: partial_failed\nFAILED: failed' DEFAULT 'uploading',
    `error_message` LONGTEXT,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
) CHARACTER SET utf8mb4 COMMENT='文档批次表';
        ALTER TABLE `documents` ADD CONSTRAINT `fk_document_document_55cd27b9` FOREIGN KEY (`batch_id`) REFERENCES `document_batches` (`id`) ON DELETE CASCADE;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE `documents` DROP FOREIGN KEY `fk_document_document_55cd27b9`;
        ALTER TABLE `documents` DROP COLUMN `batch_id`;
        ALTER TABLE `documents` DROP COLUMN `content_type`;
        ALTER TABLE `documents` DROP COLUMN `file_size`;
        DROP TABLE IF EXISTS `document_batches`;"""
