<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8 justify-end">
      <el-col :span="10">
        <el-button type="info" plain icon="Upload" @click="handleImport">导入</el-button>
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete" style="margin-left: 20px;">批量删除</el-button>
      </el-col>
    </el-row>

    <div v-if="reconscaseList.length > 0">
      <el-table border v-loading="loading" :data="reconscaseList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55px" align="center" />
        <el-table-column label="序号" type="index" width="130px" align="center" :show-overflow-tooltip="true" />
        <el-table-column label="文本标题" align="center" prop="name" />
        <el-table-column label="文件大小" align="center" prop="file_size">
          <template #default="scope">
            {{ formatFileSize(scope.row.file_size) }}
          </template>
        </el-table-column>
        <el-table-column label="导入时间" align="center" prop="process_begin_at">
          <template #default="scope">
            {{ parseTime(scope.row.process_begin_at) }}
          </template>
        </el-table-column>
        <el-table-column label="文档处理状态" align="center" prop="run">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.run)">{{ getStatusText(scope.row.run) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button link type="warning" @click="retryFailedDocument(scope.row.id)" size="small" v-if="scope.row.run === 'FAILED'">重试</el-button>
            <el-button link type="danger" @click="handleDelete(scope.row)" size="small">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 文件上传对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="700px" append-to-body @close="handleUploadDialogClose">
      <div class="upload-container">
        <el-upload
          class="upload-demo"
          drag
          multiple
          :file-list="fileList"
          :auto-upload="false"
          :on-change="handleChange"
          :on-remove="handleRemove"
          :before-upload="beforeUpload"
          accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.xlsm"
          :aria-label="'上传文件，支持' + fileValidation.allowedTypes.join('、') + '格式'"
          role="button"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            拖拽文件到此处或 <em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持 PDF、Word、Excel、PowerPoint 等格式，最多{{fileValidation.maxCount}}个文件，单文件最大{{Math.round(fileValidation.maxSize / 1024 / 1024)}}MB
            </div>
          </template>
        </el-upload>
        

        
        <!-- 文件列表统计 -->
        <div v-if="fileList.length > 0" class="file-stats">
          <el-text type="info" size="small">
            已选择 {{ fileList.length }} 个文件，总大小：{{ formatFileSize(fileList.reduce((sum, f) => sum + (f.raw?.size || 0), 0)) }}
          </el-text>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="submitFileForm" type="primary" :loading="loadingUpload" :disabled="fileList.length === 0">
            {{ loadingUpload ? '上传中...' : '开始上传' }}
          </el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ReconsCase">
import { deletefile, getfiles, uploadFileKnow, retryDocument } from "@/api/data/index.js";
import { ref, reactive, getCurrentInstance, toRefs, onMounted, onUnmounted } from "vue";
import { parseTime } from "@/utils/ruoyi";

const { proxy } = getCurrentInstance();
const reconscaseList = ref([]);
const loading = ref(false);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

// 状态轮询管理
const statusPolling = reactive({
  interval: null,
  isActive: false,
  start() {
    if (this.isActive) return;
    this.isActive = true;
    this.interval = setInterval(() => {
      this.checkPendingDocuments();
    }, 5000);
  },
  stop() {
    if (this.interval) {
      clearInterval(this.interval);
      this.interval = null;
      this.isActive = false;
    }
  },
  async checkPendingDocuments() {
    const pendingDocs = reconscaseList.value.filter(
      doc => ['PENDING', 'PROCESSING', 'QUEUED', 'INDEXING'].includes(doc.run)
    );
    
    if (pendingDocs.length > 0) {
      await getList();
    } else {
      this.stop();
    }
  }
});

// 文件验证配置
const fileValidation = {
  maxSize: 100 * 1024 * 1024, // 100MB
  allowedTypes: ['.pdf', '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx', '.xlsm'],
  maxCount: 50
};

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 20,
  },
});

const { queryParams } = toRefs(data);

function getList() {
  loading.value = true;
  const params = {
    page: queryParams.value.pageNum,
    page_size: queryParams.value.pageSize
  };
  
  getfiles(params)
    .then((response) => {
      reconscaseList.value = response.items;
      total.value = response.total;
      
      // 检查是否有待处理的文档，如果有则启动状态轮询
      const hasPendingDocs = response.items.some(
        doc => ['PENDING', 'PROCESSING', 'QUEUED', 'INDEXING'].includes(doc.run)
      );
      
      if (hasPendingDocs) {
        statusPolling.start();
      } else {
        statusPolling.stop();
      }
    })
    .catch((error) => {
      ErrorHandler.handle(error, '获取文档列表失败：');
    })
    .finally(() => {
      loading.value = false;
    });
}

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

async function handleDelete(row) {
  let itemsToDelete;
  let idsToDelete;
  
  if (row && row.id) {
    itemsToDelete = [row];
    idsToDelete = [row.id];
  } else {
    itemsToDelete = reconscaseList.value.filter(item => ids.value.includes(item.id));
    idsToDelete = ids.value;
  }
  
  if (idsToDelete.length === 0) {
    proxy.$modal.msgWarning("请选择要删除的文档");
    return;
  }
  
  // 增强的删除确认对话框
  const isMultiple = idsToDelete.length > 1;
  const fileNames = itemsToDelete.map(item => item.name).join('、');
  const confirmMessage = `确认删除以下${idsToDelete.length}个文档吗？\n\n${fileNames}\n\n此操作不可撤销！`;
  
  try {
    await proxy.$modal.confirm(confirmMessage, '删除确认', {
      type: 'warning',
      confirmButtonText: '确认删除',
      cancelButtonText: '取消'
    });
    
    loading.value = true;
    
    // 批量删除进度指示
    if (isMultiple) {
      for (let i = 0; i < idsToDelete.length; i++) {
        try {
          await deletefile(idsToDelete[i]);
          showBatchProgress('删除', idsToDelete.length, i + 1);
        } catch (error) {
          ErrorHandler.handle(error, `删除文档 ${itemsToDelete[i].name} 失败：`);
        }
      }
    } else {
      await deletefile(idsToDelete[0]);
    }
    
    proxy.$modal.msgSuccess(`成功删除 ${idsToDelete.length} 个文档`);
    await getList();
    
  } catch (error) {
    if (error !== 'cancel') {
      ErrorHandler.handle(error, '删除操作失败：');
    }
  } finally {
    loading.value = false;
  }
}

const upload = reactive({
  open: false,
  title: "导入信息",
  isUploading: false
});

function handleImport() {
  upload.title = "用户导入";
  upload.open = true;
}

const fileList = ref([]);
const loadingUpload = ref(false);

function submitFileForm() {
  if (fileList.value.length === 0) {
    proxy.$modal.msgError("请选取文件后再上传");
    return;
  }

  // 验证所有文件
  const validationResults = fileList.value.map(file => validateFile(file.raw));
  const invalidFiles = validationResults.filter(result => !result.valid);
  
  if (invalidFiles.length > 0) {
    proxy.$modal.msgError(invalidFiles[0].message);
    return;
  }

  const formData = new FormData();
  let validFileCount = 0;
  
  fileList.value.forEach((file) => {
    if (file && file.raw && file.raw instanceof File) {
      formData.append("files", file.raw);
      validFileCount++;
    }
  });
  
  if (validFileCount === 0) {
    proxy.$modal.msgError("没有有效的文件可以上传");
    return;
  }
  
  const batchName = `批次上传_${new Date().toLocaleString()}`;
  formData.append("batch_name", batchName);
  
  loadingUpload.value = true;
  upload.isUploading = true;

  uploadFileKnow(formData)
    .then((res) => {
      if (res && (res.success === true || res.success === undefined)) {
        const uploadedFiles = res.uploaded_files || res.data?.uploaded_files || [];
        
        proxy.$modal.msgSuccess({
          message: `文件上传成功！共上传 ${uploadedFiles.length} 个文件，正在后台处理中...`,
          type: 'success',
          duration: 3000
        });
        
        upload.open = false;
        handleUploadDialogClose();
        getList();
        
      } else {
        const errorMsg = res.message || res.data?.message || "上传失败";
        proxy.$modal.msgError(errorMsg);
      }
    })
    .catch((err) => {
      ErrorHandler.handle(err, '文件上传失败：');
      fileList.value = [];
    })
    .finally(() => {
      loadingUpload.value = false;
      upload.isUploading = false;
    });
}

function handleChange(file, fileListParam) {
  // 检查文件数量限制
  if (fileListParam.length > fileValidation.maxCount) {
    proxy.$modal.msgError(`最多只能选择 ${fileValidation.maxCount} 个文件`);
    return;
  }
  
  // 验证新添加的文件
  if (file && file.raw) {
    const validation = validateFile(file.raw);
    if (!validation.valid) {
      proxy.$modal.msgError(validation.message);
      return;
    }
  }
  
  // 过滤有效的文件
  const validFiles = fileListParam.filter(f => {
    if (!f || !f.raw || !(f.raw instanceof File)) {
      return false;
    }
    const validation = validateFile(f.raw);
    return validation.valid;
  });
  
  fileList.value = validFiles;
  
  // 显示文件统计信息
  if (validFiles.length > 0) {
    const totalSize = validFiles.reduce((sum, f) => sum + f.raw.size, 0);
    console.log(`已选择 ${validFiles.length} 个文件，总大小：${formatFileSize(totalSize)}`);
  }
}

function handleRemove(file, fileListParam) {
  console.log("文件移除:", file, fileListParam);
  fileList.value = fileListParam;
}

function beforeUpload(file) {
  const validation = validateFile(file);
  if (!validation.valid) {
    proxy.$modal.msgError(validation.message);
    return false;
  }
  return true;
}



// 处理上传对话框关闭事件
function handleUploadDialogClose() {
  // 清空文件列表
  fileList.value = [];
  // 重置上传状态
  upload.isUploading = false;
  loadingUpload.value = false;
}

// 状态文本转换
function getStatusText(status) {
  const statusMap = {
    'WATTING': '等待处理',
    'PENDING': '等待处理',
    'PROCESSING': '处理中',
    'COMPLETED': '已完成',
    'FAILED': '处理失败',
    'UPLOADED': '已上传',
    'QUEUED': '队列中',
    'INDEXING': '索引中',
    'WATTING': '等待处理'
  };
  return statusMap[status] || status;
}

// 统一文件验证逻辑
function validateFile(file) {
  if (!file) {
    return { valid: false, message: '文件对象无效' };
  }
  
  const validations = [
    {
      condition: file.size > fileValidation.maxSize,
      message: `文件 ${file.name} 超过${Math.round(fileValidation.maxSize / 1024 / 1024)}MB限制`
    },
    {
      condition: !fileValidation.allowedTypes.includes(getFileExtension(file.name)),
      message: `文件 ${file.name} 格式不支持，请选择${fileValidation.allowedTypes.join('、')}格式`
    }
  ];
  
  for (const validation of validations) {
    if (validation.condition) {
      return { valid: false, message: validation.message };
    }
  }
  
  return { valid: true };
}

// 获取文件扩展名
function getFileExtension(filename) {
  if (!filename) return '';
  return filename.toLowerCase().substring(filename.lastIndexOf('.'));
}

// 错误处理类
class ErrorHandler {
  static handle(error, context = '') {
    const errorMap = {
      400: '请求参数错误',
      401: '未授权访问',
      403: '权限不足',
      404: '资源不存在',
      413: '文件过大',
      422: '数据验证失败',
      500: '服务器内部错误',
      502: '网关错误',
      503: '服务不可用'
    };
    
    let message = '未知错误';
    
    if (error.response) {
      const status = error.response.status;
      const data = error.response.data;
      
      message = errorMap[status] || `请求失败 (${status})`;
      
      if (data?.detail) {
        if (typeof data.detail === 'object') {
          message = data.detail.message || JSON.stringify(data.detail);
        } else {
          message = data.detail;
        }
      }
    } else if (error.request) {
      message = '网络连接失败，请检查网络连接';
    } else if (error.message) {
      message = error.message;
    }
    
    proxy.$modal.msgError(`${context}${message}`);
    console.error(`[${context}] Error:`, error);
  }
}

// 批量操作进度指示
function showBatchProgress(operation, total, current) {
  const percentage = Math.round((current / total) * 100);
  proxy.$message({
    message: `${operation}进度: ${current}/${total} (${percentage}%)`,
    type: 'info',
    duration: 1000
  });
}

// 重试失败的文档
async function retryFailedDocument(taskId) {
  try {
    await retryDocument(taskId, true);
    proxy.$modal.msgSuccess("重试任务已提交");
    await getList();
  } catch (error) {
    proxy.$modal.msgError("重试失败: " + error.message);
  }
}

// 获取状态对应的标签类型
function getStatusType(status) {
  const statusMap = {
    'PENDING': 'warning',
    'PROCESSING': 'primary',
    'COMPLETED': 'success',
    'FAILED': 'danger',
    'UPLOADED': 'info',
    'QUEUED': 'warning',
    'INDEXING': 'warning',
    'WATTING': 'warning',
    'uploaded': 'info',
    'updated': 'warning'
  };
  return statusMap[status] || 'info';
}

// 格式化文件大小
function formatFileSize(bytes) {
  if (!bytes || bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 组件挂载时启动
onMounted(() => {
  getList();
});

// 组件卸载时清理资源
onUnmounted(() => {
  statusPolling.stop();
});
</script>

<style scoped>
.upload-container {
  padding: 20px 0;
}

.upload-demo {
  margin-bottom: 20px;
}



.file-stats {
  margin: 15px 0;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

/* 操作按钮间距 */
.el-table .el-button + .el-button {
  margin-left: 5px;
}

/* 文件列表优化 */
.el-upload-list {
  max-height: 200px;
  overflow-y: auto;
}

/* 对话框优化 */
.el-dialog__body {
  max-height: 70vh;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .el-dialog {
    width: 95% !important;
    margin: 5vh auto;
  }
  
  .upload-container {
    padding: 10px;
  }
}

@media (max-width: 480px) {
  .el-dialog {
    width: 98% !important;
    margin: 2vh auto;
  }
}
</style>