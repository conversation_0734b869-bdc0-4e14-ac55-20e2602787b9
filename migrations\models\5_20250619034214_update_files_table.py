from tortoise import BaseD<PERSON>sync<PERSON><PERSON>


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS `documents` (
    `id` INT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `uuid` CHAR(36) NOT NULL UNIQUE,
    `original_filename` VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    `minio_path` VARCHAR(255) NOT NULL,
    `task_status` VARCHAR(10) NOT NULL COMMENT 'WATTING: waiting\nPROCESSING: processing\nINDEXING: indexing\nCOMPLETED: completed\nFAILED: failed' DEFAULT 'waiting',
    `ali_task_id` VARCHAR(255),
    `ali_task_status` VARCHAR(10) NOT NULL COMMENT 'PENDING: pending\nPROCESSING: processing\nSUCCESS: success\nFAILED: failed' DEFAULT 'pending',
    `dify_dataset_id` VARCHAR(255),
    `dify_document_id` VARCHAR(255),
    `dify_batch_id` VARCHAR(255),
    `dify_indexing_status` VA<PERSON>HA<PERSON>(9) NOT NULL COMMENT 'QUEUING: queuing\nINDEXING: indexing\nCOMPLETED: completed\nERROR: error' DEFAULT 'queuing',
    `error_message` LONGTEXT,
    `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
) CHARACTER SET utf8mb4;
        DROP TABLE IF EXISTS `files`;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS `documents`;"""
